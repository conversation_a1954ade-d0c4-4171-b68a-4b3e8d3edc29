import {logger} from "../shared/logger-client.js";
import { combineMessages } from '../shared/message-combiner.js';

if (!window.rc_requests) {
class RequestsActions {
  constructor() {
    this.abilities = window.abilities || null;
    this.elements = this.abilities.elements || null;
    this.instructionsStorage = window.storageModule?.instructionStorage || null;
  }

  async log(operation, message, level = 'log', details) {
    try {
      if (chrome && chrome.runtime && !chrome.runtime.lastError) {
        await chrome.runtime.sendMessage({ action: 'LOG', operation, message, level, details });
      }
    } catch (_) {
    }
  }
  async wait(seconds) {
    if (window.clockModule?.instance?.wait) {
      return window.clockModule.instance.wait(seconds);
    }
    // Fallback to SerenTimingConfig if clock module is not available
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }
  
  async waitForStep(stepName) {
    return SerenTimingConfig.waitForStep(stepName);
  }
  async sendWelcomeMessage(roomId) {
    const welcomeMessages = ["Hey", "What's up?", "How's it going?", "Hi there","Hi?","Hi"]
    const text = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)]
    try {
      if (!this.instructionsStorage) {
        await this.log('Sailor.WelcomeMessage', 'instructionsStorage not available', 'error');
        return false;
      }
      const instruction = {
        id: `welcome_${Date.now()}`,
        type: 'sendMessage',
        room_id: roomId,
        content: {
          text: { value: text }
        },
        created_at: Date.now()
      };
      await this.instructionsStorage.addToExecutionQueue([instruction]);
      await this.log('Sailor.WelcomeMessage', `Enqueued welcome message for room ${roomId}: ${text}`);
      return true;
    } catch (error) {
      await this.log('Sailor.WelcomeMessage', `Error enqueuing welcome message: ${error.message}`, 'error');
      return false;
    }
  }
  async clickRequestRoom(roomId) {
    try {
      this.abilities.scroll.toTop();
      await this.waitForStep('afterReturnWait');
      const scroller = this.elements.virtualScrollScroller();
      let room = null;
      if (roomId) {
        try {
          const acceptBtn = this.elements.acceptRequestButton(roomId);
          if (acceptBtn) {
            acceptBtn.click();
            return roomId;
          }
        } catch {}
        room = scroller?.querySelector('rs-rooms-nav-room');
        if (!room) return null;
      }
      const navButton = room.shadowRoot?.querySelector('a');
      navButton?.click();
      return room.getAttribute('name') || room.getAttribute('room');
    } catch {
      return null;
    }
  }
  async goToRequestsPage() {
    try {
      await this.waitForStep('waitBeforeAccept');
      await this.goBackToMain();
      await this.waitForStep('afterReturnWait');
      
      const requestsButton = this.elements.requestsButton();
      const badge = requestsButton?.querySelector('rs-notifications-badge');
      const count = badge ? parseInt(badge.getAttribute('count') || '0', 10) : 0;
      if (count> 0) {
        requestsButton.querySelector('div[tabindex="0"]')?.click();
      }
      else
      {
        await this.log('Sailor.AcceptInvites', `All requests is accepted`, 'warn',error);
      }
      return count
    }
    catch (error) {
      await this.log('Sailor.AcceptInvites', `No Requests to accept on the page`, 'warn',error);
      return 0
    }
  }
  async acceptOrIgnoreRequest(needAccept=true) {
    try {
      const roomOverlay = this.elements.roomOverlay();
      if (!roomOverlay) {
        await this.log('Sailor.AcceptInvites', 'Room overlay not found', 'warn');
        return false;
      }
      
      await this.waitForStep('afterClickWait');
      const roomInviteControl = roomOverlay.querySelector('rs-room-invite')
      if (!roomInviteControl) {
        await this.log('Sailor.AcceptInvites', 'Room invite control not found', 'warn')
        return false
      }
      
      const shadowRoot = roomInviteControl.shadowRoot
      if (!shadowRoot) {
        await this.log('Sailor.AcceptInvites', 'Shadow root not found', 'warn')
        return false
      }
      
      const buttonText = needAccept ? 'Accept' : 'Ignore'
      const buttons = Array.from(shadowRoot.querySelectorAll('button'))
      const button = buttons.find(btn => btn.textContent.trim() === buttonText)
      
      if (button) {
        button.click()
        return true
      }
      
      await this.log('Sailor.AcceptInvites', `No ${buttonText} button found`, 'warn')
      return false
    }
    catch (acceptError) {
      await this.log('Sailor.ChooseRequestToAccept', `Error clicking accept button: ${acceptError.message}`, 'warn');
      return false
    }
  }
  async selectRequest() {
    this.elements.requestsView()
  }

  async goBackToMain(){
    try{
      const backButton = this.elements.backToMain()
      if (backButton) {
        backButton.click();
        await this.wait(0.5)
      }
    }
    catch (e) {

    }
  }
  async saveAcceptedInvite(senderName) {
    const { acceptedInvitations = [] } = await chrome.storage.local.get('acceptedInvitations');
    const filtered = acceptedInvitations.filter(invite => invite.senderName !== senderName);
    const newInvite = { senderName, timestamp: new Date().toISOString(), type: 'invite' };
    await chrome.storage.local.set({
      acceptedInvitations: [...filtered, newInvite],
      lastInviteTime: new Date().toISOString()
    });
    await this.log('Sailor.AcceptInvites', `Saved invite for ${senderName} to storage`);
  }

  async processInvite(serenName, roomId, needAccept = true) {
    try {
      // B-1: Navigate to requests page
      const reqCount = await this.goToRequestsPage();
      if (!reqCount) {
        return { success: false, error: 'No pending requests found' };
      }
      
      // B-2 & B-3: Wait and click request room
      await this.waitForStep('afterNavigationWait');
      const _roomId = await this.clickRequestRoom(roomId);
      
      if (!_roomId || _roomId !== roomId) {
        return { success: false, error: 'Requested room not found' };
      }
      
      // B-4: Wait after clicking request
      await this.waitForStep('afterClickWait');
      
      // Get room info
      const roomInfo = await window.rc_rooms.query.get_peek_space_info(roomId, serenName);
      if (!roomInfo) {
        return { success: false, error: 'There are no room with this id in database' };
      }
      // B-5: Accept/Ignore request
      const actionResult = await this.acceptOrIgnoreRequest(needAccept);
      if (!actionResult) {
        return { success: false, error: 'Could not process request' };
      }
      
      let lastMessage = [];
      let totalSentCount = 0;
      
      if (needAccept && actionResult) {
        // B-6: Handle welcome message or existing messages
        if (Array.isArray(roomInfo.messages) && roomInfo.messages.length > 0) {
          lastMessage = combineMessages(roomInfo.messages);
          if (lastMessage.length > 0) {
            const extraIds = roomInfo.messages.map(msg => msg.message_id);
            const response = await chrome.runtime.sendMessage({
              type: 'sendObservationsToServer',
              lastMessage,
              extraIds
            });
            totalSentCount = response?.totalSentCount || 0;
          }
        } else {
          await this.waitForStep('sendWelcomeMessage');
          await this.sendWelcomeMessage(roomInfo.room_id);
        }
        
        // B-7: Save accepted invite
        await this.saveAcceptedInvite(roomInfo.senderName);
      }
      
      // B-8: Return to main chat
      await this.goBackToMain();
      await this.waitForStep('afterReturnWait');
      
      // B-9: Final wait after accepting
      await this.waitForStep('waitAfterAccept');
      return {
        success: actionResult,
        roomId: roomInfo.room_id,
        roomData: roomInfo,
        lastMessage,
        totalSentCount,
        message: actionResult ? `Successfully accepted request` : `Could not process request`
      };
    } catch (error) {
      await this.log('Sailor.AcceptInvites', `Error accepting requests: ${error.message}`, 'error');
      await this.goBackToMain();
      return { success: false, error: error.message };
    }
  }
}
const requestActions = new RequestsActions()
window.rc_requests = { actions: requestActions };
}



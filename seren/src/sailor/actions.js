if (!window.rc_actions) {

  class ContentMediaClient {
    static async getMedia(key) {
      // Wrap sendMessage in a Promise so we can await it
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
            { action: 'getMedia', key },
            (res) => {
              if (chrome.runtime.lastError) {
                return reject(chrome.runtime.lastError);
              }
              resolve(res);
            }
        );
      });

      // Check for success / error
      if (!response.success) {
        throw new Error(response.error);
      }
      return response.blob;
    }
  }
  class ChatActions {
  static _locked = false
  static acquireLock() {
    if (this._locked) return false
    this._locked = true
    return true
  }
  static releaseLock() {
    this._locked = false
  }
    constructor() {
      this.instructionsStorage = window.storageModule?.instructionStorage || null;
      this.abilities = window.abilities || null;
      this.elements = this.abilities.elements || null;
      this.isTabVisible = !document.hidden;
      this.lastTabVisibilityChange = Date.now();
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
      this.pendingSends = new Map();
    }
    _hashMessage(room_id, messageText) {
      return `${room_id}_${messageText.slice(0, 50)}`;
    }
    async log(operation, message, level = 'log',details) {
      try {
        if (chrome && chrome.runtime && !chrome.runtime.lastError) {
          await chrome.runtime.sendMessage({ action: 'LOG', operation, message, level, details });
        }
      } catch (_) {
        // Silent failure - logging should never break functionality
      }
    }
    async handleVisibilityChange() {
      const isNowVisible = !document.hidden;
      const now = Date.now();
      const visibilityChangeDelay = now - this.lastTabVisibilityChange;
      const formattedDelay = (visibilityChangeDelay / 1000).toFixed(2);
      await this.log('RC-Space.PageState', `Chat Page visibility changed: ${this.isTabVisible} -> ${isNowVisible}, after ${formattedDelay}`);
      if (!this.isTabVisible && isNowVisible) {
        if (visibilityChangeDelay > 100000) {
          await this.log('RC-Space.Actions', 'Chat Page was inactive for extended period','warn',{visibilityChangeDelay});
        }
      }
      this.isTabVisible = isNowVisible;
      this.lastTabVisibilityChange = now;
    }

    async wait(seconds) {
      if (window.clockModule?.instance?.wait) {
        return window.clockModule.instance.wait(seconds);
      }
      // Fallback to SerenTimingConfig if clock module is not available
      return new Promise(resolve => setTimeout(resolve, seconds * 1000));
    }
    
    async waitForStep(stepName) {
      const timingConfig = SerenTimingConfig.getStepTiming(stepName) || {};
      const delay = timingConfig.delay || 0;
      await this.wait(delay);
    }

    async scrollAndFindRoom(roomName) {
      try {
        const roomsNav = this.elements.roomsNav();
        const virtualScroll = roomsNav.querySelector('rs-virtual-scroll');
        const scrollableElement = virtualScroll;
        this.abilities.scroll.toTop();
        await this.waitForStep('scrollToTop');

        const roomHeight = 80;
        const roomsPerPage = Math.floor(scrollableElement.clientHeight / roomHeight) || 1;
        const pageIncrement = roomsPerPage * roomHeight;
        const maxPageScrollAttempts = 1000; // Increased max attempts
        let pageScrollAttempts = 0;
        const totalHeight = scrollableElement.scrollHeight;
        let targetRoom = null;

        // Prepare encoded room id for URL comparison
        const encodedRoomId = encodeURIComponent(roomName);

        while (scrollableElement.scrollTop < totalHeight && pageScrollAttempts < maxPageScrollAttempts) {
          targetRoom = virtualScroll.shadowRoot.querySelector(`rs-rooms-nav-room[room="${roomName}"]`);
          if (targetRoom) {
            const navButton = targetRoom.shadowRoot?.querySelector('a');
            if (navButton) {
              // Save URL before clicking
              const oldUrl = window.location.href;
              navButton.click();

              // Wait for URL to match expected pattern
              const urlPattern = new RegExp(`https://chat.reddit.com/room/(${encodedRoomId})`);
              let found = false;
              const startTime = Date.now();
              const maxWaitTime = SerenTimingConfig.getStepTiming('roomNavigation').timeout || 3000;
              
              // Wait for URL to update with timeout
              while (Date.now() - startTime < maxWaitTime) {
                await this.wait(0.2);
                if (urlPattern.test(window.location.href)) {
                  found = true;
                  break;
                }
              }
              if (!found) {
                await this.log('Sailor.ScrollAndFindRoom', `URL did not update to expected room: "${roomName}", got: ${window.location.href}`, 'warn');
                return null;
              }
              await this.waitForStep('afterRoomClick');
              return targetRoom;
            }
          }
          const previousScrollTop = scrollableElement.scrollTop;
          scrollableElement.scrollTop += pageIncrement;
          await this.waitForStep('sidebarScrollHop');
          pageScrollAttempts++;
          if (previousScrollTop === scrollableElement.scrollTop) {
            await this.log('Sailor.ScrollAndFindRoom', 'Reached end of scroll during page scrolling', 'info');
            break;
          }
        }
        scrollableElement.scrollTop = totalHeight;
        await this.waitForStep('maxSidebarScroll');
        targetRoom = virtualScroll.shadowRoot.querySelector(`rs-rooms-nav-room[room="${roomName}"]`);
        if (targetRoom) {
          const navButton = targetRoom.shadowRoot?.querySelector('a');
          if (navButton) {
            // Save URL before clicking
            const oldUrl = window.location.href;
            navButton.click();
            // Wait for URL to match expected pattern
            const urlPattern = new RegExp(`/room/(${encodedRoomId})$`);
            let found = false;
            let tries = 0;
            while (tries < 20) {
              await this.wait(0.2);
              const newUrl = window.location.href;
              if (newUrl !== oldUrl && urlPattern.test(newUrl)) {
                found = true;
                break;
              }
              tries++;
            }
            if (!found) {
              await this.log('Sailor.ScrollAndFindRoom', `URL did not update to expected room: "${roomName}", got: ${window.location.href}`, 'warn');
              return null;
            }
            await this.waitForStep('afterRoomClick');
            await this.log('Sailor.ScrollAndFindRoom', `Room found after scrolling to bottom: ${roomName}`, 'success');
            return targetRoom;
          }
        }
        await this.log('Sailor.ScrollAndFindRoom', `Room not found: ${roomName}`, 'error');
        return null;
      } catch (error) {
        await this.log('Sailor.scrollAndFindRoom', `Error finding room: ${error.message}`, 'error');
        return null;
      }
    }
    
    async isMessageAlreadySentInDb(action, needLog = true){
      const sentInDb = await window.rc_rooms.query.check_message_is_sent(action.room_id, action);
      if (sentInDb) {
        if (needLog) {
          const preview = action.content.text.value.trim().substring(0, 30);
          await this.log('Sailor.ExecuteActions', `Duplicate detected via DB: "${preview}..."`, 'warn');
        }
        return true;
      }
    }
    async acceptInvite(action) {
      try {
        const result = await window.rc_requests?.actions?.processInvite(action.serenName,action.room_id, action.needAccept);
        if (!result.success) {
          await this.log('Sailor.acceptInvite', `Failed to accept invite: ${result.error}`, 'warn');
          return false;
        }
        return true;
      } catch (error) {
        await this.log('Sailor.acceptInvite', `Error accepting invite: ${error.message}`, 'error');
        return false;
      }
    }

    async isMessageAlreadySent(action, needLog = true) {
      try {
        if (!action || !action.room_id || !action.content || !action.content.text || typeof action.content.text.value !== 'string') {
          // Invalid action object; treat as not sent.
          return false;
        }
        const messageText = action.content.text.value.trim();
        const pendingKey = this._hashMessage(action.room_id, messageText);
        if (this.pendingSends.has(pendingKey)) {
          if (needLog) {
            await this.log('Sailor.ExecuteActions', `Duplicate detected via pendingSends: "${messageText.slice(0,30)}..."`, 'warn');
          }
          return true;
        }
        // 1. Database-level check for duplication.
       if (await this.isMessageAlreadySentInDb(action,needLog))
         return true;

        // 2. UI-level check against the last message in the chat.
        const lastMessage = this.getLastMessage();
        const normalizeText = (txt) =>
            txt
                .replace(/[\s\n]+/g, ' ') // collapse spaces/newlines
                .trim()
                .toLowerCase();
        if (
            lastMessage &&
            lastMessage.content &&
            lastMessage.content.text &&
            typeof lastMessage.content.text.value === 'string'
        ) {
          const messageToSend = action.content.text.value;
          const lastMessageText = lastMessage.content.text.value;

          const normSend = normalizeText(messageToSend);
          const normLast = normalizeText(lastMessageText);

          // Similarity check: treat messages as duplicate when normalized texts share ≥ 90 % similarity
          const levenshtein = (a, b) => {
            const rows = a.length + 1;
            const cols = b.length + 1;
            const dp = Array.from({ length: rows }, () => new Array(cols).fill(0));

            for (let i = 0; i < rows; i++) dp[i][0] = i;
            for (let j = 0; j < cols; j++) dp[0][j] = j;

            for (let i = 1; i < rows; i++) {
              for (let j = 1; j < cols; j++) {
                const cost = a[i - 1] === b[j - 1] ? 0 : 1;
                dp[i][j] = Math.min(
                  dp[i - 1][j] + 1,       // deletion
                  dp[i][j - 1] + 1,       // insertion
                  dp[i - 1][j - 1] + cost // substitution
                );
              }
            }
            return dp[a.length][b.length];
          };

          const similarity = (a, b) => {
            const longerLen = Math.max(a.length, b.length);
            if (longerLen === 0) return 1; // both strings empty
            const distance = levenshtein(a, b);
            return (longerLen - distance) / longerLen;
          };

          if (
            normSend.length > 10 &&           // avoid matching very short strings
            normLast.length > 10 &&
            similarity(normSend, normLast) >= 0.9
          ) {
            if (needLog) {
              await this.log(
                'Sailor.ExecuteActions',
                `Duplicate detected (≥90% similarity) on page: "${normSend.slice(0, 30)}..."`,
                'warn'
              );
            }
            return true;
          }
        }

        return false;
      } catch (error) {
        // Log unexpected errors and treat as not already sent to allow retry.
        await this.log('Sailor.ExecuteActions', `Error in isMessageAlreadySent: ${error.message}`, 'error');
        return false;
      }
    }

    async execActions() {
      let instructions = await this.instructionsStorage.getInstructionsQueue();
      if (!instructions.length) return;
      
      // Deduplicate: Map by room_id + normalized message text
      const seen = new Set();
      const deduped = [];
      const toRemove = [];

      // A-0: Duplicate check in queue
      for (const action of instructions) {
        if (action.content?.text?.value && action.room_id) {
          const normText = action.content.text.value.trim();
          const key = `${action.room_id}___${normText}`;
          if (seen.has(key)) {
            toRemove.push(action.id);
            continue;
          }
          seen.add(key);
        }
        deduped.push(action);
      }

      for (const id of toRemove) {
        await this.instructionsStorage.markAsProcessed(id, 'duplicate_in_queue');
      }

      instructions = deduped;
      await this.log('Sailor.Queue', `Queued for execution ${instructions.length} instructions`,'info',{messages:instructions});
      
      // Acquire lock with timeout
      const lockAcquired = await Promise.race([
        (async () => {
          while (!window.rc_actions.acquireLock()) {
            await this.wait(0.1);
          }
          return true;
        })(),
        this.wait(5).then(() => false)
      ]);
      
      if (!lockAcquired) {
        await this.log('Sailor.Queue', 'Failed to acquire lock after timeout', 'warn');
        return {result: "Failed to acquire lock"};
      }
      
      try {
        while (instructions.length) {
          const action = instructions.pop();
          
          try {
            // Handle accept invite action
            if (action.type === 'acceptInvite') {
              await this.waitForStep('execActionsWait');
              if (await this.acceptInvite(action)) {
                await this.instructionsStorage.markAsProcessed(action.id, 'accepted', 'invite');
              } else {
                await this.instructionsStorage.markAsProcessed(action.id, 'failed_to_accept', 'invite');
              }
              await this.waitForStep('waitAfterAccept');
              continue;
            }
            
            // Check if message was already sent
            if (await this.isMessageAlreadySentInDb(action, true)) {
              await this.instructionsStorage.markAsProcessed(action.id, 'already_sent');
              continue;
            }
            
            // Navigate to room
            if (!await this.scrollAndFindRoom(action.room_id)) {
              await this.instructionsStorage.markAsProcessed(action.id, 'room_not_found');
              continue;
            }
            
            await this.waitForStep('execActionsWait');
            
            // Handle file upload
            if (action.content?.media?.storageKey) {
              const base64Data = await ContentMediaClient.getMedia(action.content.media.storageKey);
              await this.sendFile(base64Data, action.content.text?.value);
              await this.instructionsStorage.markAsProcessed(action.id, 'file_sent');
            }
            // Handle text message
            else if (action.content?.text?.value) {
              const messageText = action.content.text.value.trim();
              const pendingKey = this._hashMessage(action.room_id, messageText);
              
              if (await this.isMessageAlreadySent(action)) {
                await this.instructionsStorage.markAsProcessed(action.id, 'already_sent');
                continue;
              }
              
              this.pendingSends.set(pendingKey, Date.now());
              
              try {
                const skipSending = action.is_simulation;
                const sendResult = await this.sendMessage(messageText, skipSending);
                
                if (!sendResult.success) {
                  this.pendingSends.delete(pendingKey);
                  continue;
                }
                
                if (!action.is_simulation) {
                  await this.waitForMessageInDOM(action.room_id, messageText);
                  if (await this.isMessageAlreadySent(action, false)) {
                    await this.instructionsStorage.markAsProcessed(action.id, 'verified');
                  }
                } else {
                  await this.instructionsStorage.markAsProcessed(action.id, 'verified');
                }
              } finally {
                this.pendingSends.delete(pendingKey);
              }
            }
          } catch (error) {
            await this.log('Sailor.ExecuteActions', `Action failed: ${error.message}`, 'error');
            // Optionally mark as failed in storage
            // await this.instructionsStorage.markAsProcessed(action.id, 'error', error.message);
          }
        }
      } finally {
        window.rc_actions.releaseLock();
      }
    }
    async waitForMessageInDOM(room_id, messageText, timeout = 4000, interval = 300) {
      const start = Date.now();
      const timingConfig = SerenTimingConfig.getStepTiming('messageVerification') || {};
      const actualTimeout = timingConfig.timeout || timeout;
      const actualInterval = timingConfig.interval || interval;
      
      let last = '';
      while (Date.now() - start < actualTimeout) {
        // Optionally, re-navigate to room in case of UI lag
        const lastMsg = this.getLastMessage();
        if (
            lastMsg &&
            lastMsg.content &&
            lastMsg.content.text &&
            typeof lastMsg.content.text.value === 'string'
        ) {
          last = lastMsg.content.text.value.trim();
          if (last === messageText.trim()) {
            return true;
          }
        }
        await this.wait(actualInterval / 1000);
      }
      await this.log('Sailor.WaitForMessage', `Timeout waiting for "${messageText.slice(0, 30)}..." in DOM. Last found: "${last.slice(0, 30)}..."`, 'warn');
      return false;
    }
    async triggerQueue(){
      setTimeout(() => this.execActions(), 0);
    }
    async navigateToRoom(roomId) {
      try {
        await this.waitForStep('beforeRoomNavigation');
        
        const roomLink = document.querySelector(`a[href*="${roomId}"]`);
        if (!roomLink) {
          throw new Error(`Room link not found for ID: ${roomId}`);
        }
        
        roomLink.click();
        
        // Wait for navigation to complete with configurable timeout
        const navConfig = SerenTimingConfig.getStepTiming('roomNavigation') || {};
        const maxWaitTime = navConfig.timeout || 3000;
        const startTime = Date.now();
        const targetUrl = `https://chat.reddit.com/room/${encodeURIComponent(roomId)}`;
        
        while (Date.now() - startTime < maxWaitTime) {
          if (window.location.href.startsWith(targetUrl)) {
            await this.waitForStep('afterRoomNavigation');
            return true;
          }
          await this.wait(0.1);
        }
        
        throw new Error(`Navigation to room ${roomId} timed out`);
      } catch (error) {
        await this.log('Sailor.NavigateToRoom', `Error navigating to room: ${error.message}`, 'error');
        return false;
      }
    }
    async sendMessage(text, skipSending = false) {
    try {
      if (!text || typeof text !== 'string') {
        throw new Error('Invalid message text');
      }
      
      const messageInput = this.elements.messageInput();
      if (!messageInput) {
        throw new Error('Message input not found');
      }
      
      // Wait before typing
      await this.waitForStep('beforeTyping');
      
      // Type the message with configurable timing
      const typingConfig = SerenTimingConfig.getStepTiming('typing') || {};
      const charsPerSecond = typingConfig.speed || 5; // Default to 5 chars/second
      const minDelay = typingConfig.minDelay || 0.05; // 50ms between keystrokes
      
      // Simulate typing
      messageInput.value = '';
      messageInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      for (const char of text) {
        messageInput.value += char;
        messageInput.dispatchEvent(new Event('input', { bubbles: true }));
        await this.wait(Math.max(minDelay, 1 / charsPerSecond));
      }
      
      // Small delay after typing
      await this.waitForStep('afterTyping');
      
      if (skipSending) {
        return { success: true };
      }
      
      // Click send button
      const sendButton = this.elements.sendButton();
      if (!sendButton) {
        throw new Error('Send button not found');
      }
      
      // Wait before clicking send
      await this.waitForStep('beforeSendClick');
      
      sendButton.click();
      
      // Wait for message to be sent with configurable delay
      await this.waitForStep('afterSendClick');

      await this.log('Sailor.SendMessage', `Message: ${text}`);

        // Check for send error after click & wait
        if (this.hasSendErrorForLastMessage()) {
          await this.log('Sailor.SendMessage', `Send error detected for message: ${text}`, 'error');
          return { success: false, error: 'Unable to send message' };
        }
        return { success: true };
      } catch (error) {
        await this.log('Sailor', `Error sending message: ${error}`, 'error');
        return { success: false, error: error.message };
      }
    }

    async sendFile(fileData, text) {
      try {
        const dropTarget = this.elements.roomOverlay().querySelector("main");
        let file;
        if (typeof fileData === 'string') {
          file = this.abilities.media.base64ToFile(fileData);
        } else {
          file = fileData;
        }
        //rs-message-composer-attachments
        const target = this.elements.composer().querySelector('rs-message-composer-attachments').shadowRoot.querySelector('input[type="file"]')
        //await this.abilities.attachments.pickAndAttach(target,fileData);
        await this.abilities.attachments.attachFile(target,fileData)
        //media.simulateFileDrop(dropTarget, file)
        await this.wait(1.5)
        if (text) {
          await this.sendMessage(text)
        }
        this.elements.submitButton().click()
        await this.log('Sailor.SendFile', `Sent file ${text ? 'with text ' + text : ''}`,text);
        return true;
      } catch (error) {
        await this.log('Sailor.SendFile', `Error sending file: ${error.message}`, 'error');
        return false;
      }
    }

    getLastMessage() {
      // Extract time from the <time> element inside <faceplate-date>
      const roomChat = this.elements.roomOverlay()
      const timeline = roomChat.querySelector('main rs-timeline').shadowRoot
      const msgEl = timeline.querySelector('rs-virtual-scroll-dynamic').shadowRoot.querySelector('rs-timeline-event[last-live-item]').shadowRoot

      const timeEl = msgEl.querySelector('faceplate-date time');
      const time = timeEl ? timeEl.getAttribute('datetime') : '';

      const senderEl = msgEl.querySelector('.user-name');
      const sender = senderEl ? senderEl.textContent.trim() : '';

      const textEl = msgEl.querySelector('.room-message-text');
      const text = textEl ? textEl.textContent.trim() : '';
      const messageId = msgEl.host.getAttribute('data-id') || '';

      return {
        id: messageId,
        message_id: messageId,
        message_time: new Date(time).toISOString(),
        time: new Date(time).toISOString(),
        user_name: sender,
        user_id:sender,
        message_text:text,
        content:{
          type:"text",
          text:{value:text}
        }
      };
    }

    hasSendErrorForLastMessage() {
      try {
        const roomChat = this.elements.roomOverlay();
        const timeline = roomChat.querySelector('main rs-timeline').shadowRoot;
        const lastMsg = timeline.querySelector('rs-virtual-scroll-dynamic').shadowRoot.querySelector('rs-timeline-event[last-live-item]').shadowRoot;
        // The .room-message-body contains error div if present
        const msgBody = lastMsg.querySelector('.room-message-body');
        if (!msgBody) return false;
        const errorDiv = msgBody.querySelector('.error');
        if (errorDiv && errorDiv.textContent.includes('Unable to send message')) {
          return true;
        }
        return false;
      } catch (_) {
        return false;
      }
    }
  }
  const actions = new ChatActions();
  window.rc_actions = {  actions: actions, acquireLock : ChatActions.acquireLock, releaseLock: ChatActions.releaseLock };
}
